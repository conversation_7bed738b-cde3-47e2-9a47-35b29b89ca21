// Test simple pour diagnostiquer les problèmes Electron
const { app, BrowserWindow } = require('electron');
const path = require('path');

console.log('🚀 Test Electron - Démarrage...');

app.disableHardwareAcceleration();

function createWindow() {
    console.log('📱 Création de la fenêtre...');
    
    const mainWindow = new BrowserWindow({
        width: 800,
        height: 600,
        show: false,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });

    console.log('📄 Chargement de la page...');
    
    // Charger une page simple
    mainWindow.loadFile('activation.html').then(() => {
        console.log('✅ Page chargée avec succès');
        mainWindow.show();
    }).catch((error) => {
        console.error('❌ Erreur lors du chargement de la page:', error);
    });

    mainWindow.on('closed', () => {
        console.log('🔒 Fenêtre fermée');
    });
}

app.whenReady().then(() => {
    console.log('✅ Application prête');
    createWindow();

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    console.log('🚪 Toutes les fenêtres fermées');
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', () => {
    console.log('🛑 Application en cours de fermeture...');
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée non gérée:', reason);
});
