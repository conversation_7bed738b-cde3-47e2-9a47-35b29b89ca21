directories:
  output: gestionpro-installer-final
  buildResources: build
appId: com.gestionpro.app
productName: GestionPro
copyright: Copyright © 2025 GestionPro
files:
  - filter:
      - '**/*'
      - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
      - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
      - '!**/node_modules/*.d.ts'
      - '!**/node_modules/.bin'
      - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!.editorconfig'
      - '!**/._*'
      - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
      - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
      - '!**/{appveyor.yml,.travis.yml,circle.yml}'
      - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
extraResources:
  - from: database
    to: database
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
  requestedExecutionLevel: asInvoker
  artifactName: ${productName} Setup ${version}.${ext}
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: GestionPro
publish: null
electronVersion: 28.3.3
